﻿#pragma checksum "..\..\..\AdvancedSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "526EF7CF7B0BB00DC89421F6D175991709047FCB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CodeValidator.GUI.UI;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CodeValidator.GUI {
    
    
    /// <summary>
    /// AdvancedSettingsWindow
    /// </summary>
    public partial class AdvancedSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 84 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AIProviderCombo;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox APIEndpointText;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox APIKeyBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ModelNameText;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoConnectCheck;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableStreamingCheck;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DefaultLanguageCombo;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FontSizeLabel;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WordWrapCheck;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LineNumbersCheck;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MinimapCheck;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoSaveCheck;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EditorThemeCombo;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UIThemeCombo;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SyncThemesCheck;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultProjectPathText;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoLoadLastProjectCheck;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowHiddenFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\AdvancedSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableGitIntegrationCheck;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CodeValidator.GUI;component/advancedsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\AdvancedSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AIProviderCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.APIEndpointText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.APIKeyBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 4:
            this.ModelNameText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.AutoConnectCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.EnableStreamingCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.DefaultLanguageCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.FontSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 9:
            this.FontSizeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.WordWrapCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.LineNumbersCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.MinimapCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.AutoSaveCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.EditorThemeCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.UIThemeCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.SyncThemesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.DefaultProjectPathText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.AutoLoadLastProjectCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.ShowHiddenFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.EnableGitIntegrationCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

