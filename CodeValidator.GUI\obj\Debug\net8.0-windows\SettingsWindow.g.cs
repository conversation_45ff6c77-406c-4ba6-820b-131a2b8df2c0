﻿#pragma checksum "..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EC876920B73EA2B2C34D0B54E28C67E0DDBD2281"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MahApps.Metro;
using MahApps.Metro.Accessibility;
using MahApps.Metro.Actions;
using MahApps.Metro.Automation.Peers;
using MahApps.Metro.Behaviors;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;
using MahApps.Metro.Converters;
using MahApps.Metro.Markup;
using MahApps.Metro.Theming;
using MahApps.Metro.ValueBoxes;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CodeValidator.GUI {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : MahApps.Metro.Controls.MetroWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 48 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontFamilyCombo;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeCombo;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowLineNumbersCheck;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WordWrapCheck;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowWhitespaceCheck;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HighlightCurrentLineCheck;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoIndentCheck;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ConvertTabsCheck;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider TabSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableIntelliSenseCheck;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoCompleteCheck;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowParameterHintsCheck;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowQuickInfoCheck;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider CompletionDelaySlider;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AsyncNoAwaitCheck;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UnusedUsingCheck;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EmptyMethodCheck;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EmptyCatchCheck;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MagicNumberCheck;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StringConcatenationCheck;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MissingNullCheckCheck;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TODOCommentCheck;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox NotImplementedCheck;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompilationErrorCheck;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAutoFixCheck;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SafeFixOnlyCheck;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BackupBeforeFixCheck;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AnalyzeOnSaveCheck;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RealTimeAnalysisCheck;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CSharpFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox VBFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox FSharpFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox JavaScriptFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TypeScriptFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PythonFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox JavaFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CppFilesCheck;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EncodingCombo;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LineEndingCombo;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoDetectEncodingCheck;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RemoveTrailingWhitespaceCheck;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnsureFinalNewlineCheck;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MaxFileSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableCachingCheck;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ParallelAnalysisCheck;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLoggingCheck;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowDiagnosticsCheck;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CodeValidator.GUI;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FontFamilyCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.FontSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 3:
            this.ThemeCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.ShowLineNumbersCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.WordWrapCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.ShowWhitespaceCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.HighlightCurrentLineCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.AutoIndentCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.ConvertTabsCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.TabSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 11:
            this.EnableIntelliSenseCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.AutoCompleteCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.ShowParameterHintsCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.ShowQuickInfoCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.CompletionDelaySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 16:
            this.AsyncNoAwaitCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.UnusedUsingCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.EmptyMethodCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.EmptyCatchCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.MagicNumberCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.StringConcatenationCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.MissingNullCheckCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.TODOCommentCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.NotImplementedCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.CompilationErrorCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.EnableAutoFixCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.SafeFixOnlyCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 28:
            this.BackupBeforeFixCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 29:
            this.AnalyzeOnSaveCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.RealTimeAnalysisCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 31:
            this.CSharpFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.VBFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.FSharpFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 34:
            this.JavaScriptFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.TypeScriptFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 36:
            this.PythonFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 37:
            this.JavaFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 38:
            this.CppFilesCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 39:
            this.EncodingCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 40:
            this.LineEndingCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 41:
            this.AutoDetectEncodingCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 42:
            this.RemoveTrailingWhitespaceCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 43:
            this.EnsureFinalNewlineCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 44:
            this.MaxFileSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 45:
            this.EnableCachingCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 46:
            this.ParallelAnalysisCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 47:
            this.EnableLoggingCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 48:
            this.ShowDiagnosticsCheck = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 49:
            
            #line 246 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearCache_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            
            #line 247 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetDefaults_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            
            #line 257 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OK_Click);
            
            #line default
            #line hidden
            return;
            case 52:
            
            #line 265 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            
            #line 273 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Apply_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

