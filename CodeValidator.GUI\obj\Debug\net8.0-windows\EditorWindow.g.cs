﻿#pragma checksum "..\..\..\EditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5F26BB5A054225E096D4F338B721C191BB372D4F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Editing;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Rendering;
using ICSharpCode.AvalonEdit.Search;
using MahApps.Metro;
using MahApps.Metro.Accessibility;
using MahApps.Metro.Actions;
using MahApps.Metro.Automation.Peers;
using MahApps.Metro.Behaviors;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;
using MahApps.Metro.Converters;
using MahApps.Metro.Markup;
using MahApps.Metro.Theming;
using MahApps.Metro.ValueBoxes;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CodeValidator.GUI {
    
    
    /// <summary>
    /// EditorWindow
    /// </summary>
    public partial class EditorWindow : MahApps.Metro.Controls.MetroWindow, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 164 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView FileTree;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentPathText;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ModelCombo;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelStatusText;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox QuickLineNumbersCheck;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox QuickWordWrapCheck;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ICSharpCode.AvalonEdit.TextEditor CodeEditor;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ChatScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ChatHistory;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ChatInput;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AgentModeToggle;
        
        #line default
        #line hidden
        
        
        #line 479 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OutputText;
        
        #line default
        #line hidden
        
        
        #line 502 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid IssuesGrid;
        
        #line default
        #line hidden
        
        
        #line 517 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox FindResultsList;
        
        #line default
        #line hidden
        
        
        #line 537 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 541 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CursorPositionText;
        
        #line default
        #line hidden
        
        
        #line 542 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectionText;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\EditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EncodingText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CodeValidator.GUI;component/editorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\EditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 55 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.New_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 56 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Open_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 57 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Save_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 58 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAs_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 60 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Exit_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 76 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Find_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 77 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Replace_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 78 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.GoToLine_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 87 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzeFile_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 88 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzeFile_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 97 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.WordWrap_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 98 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.LineNumbers_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 99 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Folding_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 108 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 109 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.About_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 156 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFolder_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.FileTree = ((System.Windows.Controls.TreeView)(target));
            
            #line 164 "..\..\..\EditorWindow.xaml"
            this.FileTree.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.FileTree_SelectedItemChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CurrentPathText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ModelCombo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 184 "..\..\..\EditorWindow.xaml"
            this.ModelCombo.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ModelCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ModelStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            
            #line 206 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ConnectOllama_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 216 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzeCode_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 224 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FixWithAI_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 232 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateDocs_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 240 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateTests_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.QuickLineNumbersCheck = ((System.Windows.Controls.CheckBox)(target));
            
            #line 245 "..\..\..\EditorWindow.xaml"
            this.QuickLineNumbersCheck.Checked += new System.Windows.RoutedEventHandler(this.QuickSetting_Changed);
            
            #line default
            #line hidden
            
            #line 245 "..\..\..\EditorWindow.xaml"
            this.QuickLineNumbersCheck.Unchecked += new System.Windows.RoutedEventHandler(this.QuickSetting_Changed);
            
            #line default
            #line hidden
            return;
            case 27:
            this.QuickWordWrapCheck = ((System.Windows.Controls.CheckBox)(target));
            
            #line 246 "..\..\..\EditorWindow.xaml"
            this.QuickWordWrapCheck.Checked += new System.Windows.RoutedEventHandler(this.QuickSetting_Changed);
            
            #line default
            #line hidden
            
            #line 246 "..\..\..\EditorWindow.xaml"
            this.QuickWordWrapCheck.Unchecked += new System.Windows.RoutedEventHandler(this.QuickSetting_Changed);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 247 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.CodeEditor = ((ICSharpCode.AvalonEdit.TextEditor)(target));
            
            #line 264 "..\..\..\EditorWindow.xaml"
            this.CodeEditor.TextChanged += new System.EventHandler(this.CodeEditor_TextChanged);
            
            #line default
            #line hidden
            
            #line 265 "..\..\..\EditorWindow.xaml"
            this.CodeEditor.PreviewKeyDown += new System.Windows.Input.KeyEventHandler(this.CodeEditor_PreviewKeyDown);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 303 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewChat_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 309 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowMemory_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 311 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowRules_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 313 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowTheme_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.ChatScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 35:
            this.ChatHistory = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 38:
            
            #line 364 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UploadFile_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            
            #line 372 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UploadImage_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            
            #line 380 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PasteToChat_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.ChatInput = ((System.Windows.Controls.TextBox)(target));
            
            #line 393 "..\..\..\EditorWindow.xaml"
            this.ChatInput.KeyDown += new System.Windows.Input.KeyEventHandler(this.ChatInput_KeyDown);
            
            #line default
            #line hidden
            return;
            case 42:
            this.AgentModeToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 421 "..\..\..\EditorWindow.xaml"
            this.AgentModeToggle.Checked += new System.Windows.RoutedEventHandler(this.AgentMode_Changed);
            
            #line default
            #line hidden
            
            #line 421 "..\..\..\EditorWindow.xaml"
            this.AgentModeToggle.Unchecked += new System.Windows.RoutedEventHandler(this.AgentMode_Changed);
            
            #line default
            #line hidden
            return;
            case 43:
            
            #line 431 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowTools_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            
            #line 443 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendChat_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.OutputText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.IssuesGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 47:
            this.FindResultsList = ((System.Windows.Controls.ListBox)(target));
            return;
            case 48:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.CursorPositionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.SelectionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.EncodingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 36:
            
            #line 336 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyChatMessage_Click);
            
            #line default
            #line hidden
            break;
            case 37:
            
            #line 337 "..\..\..\EditorWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyChatCode_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

