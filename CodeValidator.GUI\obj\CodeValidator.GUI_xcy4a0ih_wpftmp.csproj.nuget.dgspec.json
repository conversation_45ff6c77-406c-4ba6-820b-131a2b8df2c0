{"format": 1, "restore": {"D:\\CodeValidatorSuite\\CodeValidator.GUI\\CodeValidator.GUI.csproj": {}}, "projects": {"D:\\CodeValidatorSuite\\CodeValidator.Core\\CodeValidator.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CodeValidatorSuite\\CodeValidator.Core\\CodeValidator.Core.csproj", "projectName": "CodeValidator.Core", "projectPath": "D:\\CodeValidatorSuite\\CodeValidator.Core\\CodeValidator.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CodeValidatorSuite\\CodeValidator.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Build.Locator": {"target": "Package", "version": "[1.5.3, )"}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"target": "Package", "version": "[4.11.0, )"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild": {"target": "Package", "version": "[4.11.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CodeValidatorSuite\\CodeValidator.GUI\\CodeValidator.GUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CodeValidatorSuite\\CodeValidator.GUI\\CodeValidator.GUI.csproj", "projectName": "CodeValidator.GUI", "projectPath": "D:\\CodeValidatorSuite\\CodeValidator.GUI\\CodeValidator.GUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CodeValidatorSuite\\CodeValidator.GUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\CodeValidatorSuite\\CodeValidator.Core\\CodeValidator.Core.csproj": {"projectPath": "D:\\CodeValidatorSuite\\CodeValidator.Core\\CodeValidator.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AvalonEdit": {"target": "Package", "version": "[********, )"}, "MahApps.Metro": {"target": "Package", "version": "[2.4.10, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.CodeAnalysis.EditorFeatures": {"target": "Package", "version": "[2.8.2, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.ML": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.ML.AutoML": {"target": "Package", "version": "[0.21.1, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.2210.55, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}