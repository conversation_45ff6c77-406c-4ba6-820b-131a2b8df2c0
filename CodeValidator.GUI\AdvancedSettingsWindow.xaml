<Window x:Class="CodeValidator.GUI.AdvancedSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="clr-namespace:CodeValidator.GUI.UI"
        Title="⚙️ Advanced Editor Settings"
        Height="700" Width="900"
        Background="Black"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Window.Resources>
        <Style x:Key="SettingsPanelStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#2D3748" Offset="0"/>
                        <GradientStop Color="#1A202C" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#4A5568"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#60A5FA"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>

        <Style x:Key="SettingLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <Style x:Key="SettingInputStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#374151"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#6B7280"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>

        <Style x:Key="SettingComboStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="#374151"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#6B7280"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="⚙️ Advanced Editor Settings"
                   FontSize="24" FontWeight="Bold" Foreground="White"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- AI Agent Settings -->
                <Border Style="{StaticResource SettingsPanelStyle}">
                    <StackPanel>
                        <TextBlock Text="🤖 AI Agent Configuration" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Text="AI Provider:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox x:Name="AIProviderCombo" Style="{StaticResource SettingComboStyle}">
                            <ComboBoxItem Content="Ollama (Local)" IsSelected="True"/>
                            <ComboBoxItem Content="OpenAI API"/>
                            <ComboBoxItem Content="Azure OpenAI"/>
                            <ComboBoxItem Content="Anthropic Claude"/>
                            <ComboBoxItem Content="Google Gemini"/>
                            <ComboBoxItem Content="Custom API"/>
                        </ComboBox>

                        <TextBlock Text="API Endpoint:" Style="{StaticResource SettingLabelStyle}"/>
                        <TextBox x:Name="APIEndpointText" Style="{StaticResource SettingInputStyle}"
                                 Text="http://localhost:11434"
                                 ToolTip="Enter your custom API endpoint URL"/>

                        <TextBlock Text="API Key (if required):" Style="{StaticResource SettingLabelStyle}"/>
                        <PasswordBox x:Name="APIKeyBox"
                                     Background="#374151" Foreground="White" BorderBrush="#6B7280"
                                     BorderThickness="1" Padding="8" Margin="0,0,0,12" FontSize="12"
                                     ToolTip="Enter your API key for external services"/>

                        <TextBlock Text="Model Name:" Style="{StaticResource SettingLabelStyle}"/>
                        <TextBox x:Name="ModelNameText" Style="{StaticResource SettingInputStyle}"
                                 Text="llama3.2:latest"
                                 ToolTip="Specify the model to use (e.g., gpt-4, claude-3, llama3.2:latest)"/>

                        <CheckBox x:Name="AutoConnectCheck" Content="Auto-connect on startup"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                        <CheckBox x:Name="EnableStreamingCheck" Content="Enable streaming responses"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                    </StackPanel>
                </Border>

                <!-- Editor Settings -->
                <Border Style="{StaticResource SettingsPanelStyle}">
                    <StackPanel>
                        <TextBlock Text="📝 Editor Configuration" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Text="Default Language:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox x:Name="DefaultLanguageCombo" Style="{StaticResource SettingComboStyle}">
                            <ComboBoxItem Content="C#" IsSelected="True"/>
                            <ComboBoxItem Content="JavaScript"/>
                            <ComboBoxItem Content="TypeScript"/>
                            <ComboBoxItem Content="Python"/>
                            <ComboBoxItem Content="Java"/>
                            <ComboBoxItem Content="HTML"/>
                            <ComboBoxItem Content="CSS"/>
                            <ComboBoxItem Content="JSON"/>
                        </ComboBox>

                        <TextBlock Text="Font Size:" Style="{StaticResource SettingLabelStyle}"/>
                        <Slider x:Name="FontSizeSlider" Minimum="10" Maximum="24" Value="14"
                                TickFrequency="1" IsSnapToTickEnabled="True" Margin="0,0,0,12"/>
                        <TextBlock x:Name="FontSizeLabel" Text="14px" Foreground="#9CA3AF"
                                   HorizontalAlignment="Right" Margin="0,-12,0,12"/>

                        <CheckBox x:Name="WordWrapCheck" Content="Enable word wrap"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="False"/>
                        <CheckBox x:Name="LineNumbersCheck" Content="Show line numbers"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                        <CheckBox x:Name="MinimapCheck" Content="Show minimap"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                        <CheckBox x:Name="AutoSaveCheck" Content="Auto-save files"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                    </StackPanel>
                </Border>

                <!-- Theme Settings -->
                <Border Style="{StaticResource SettingsPanelStyle}">
                    <StackPanel>
                        <TextBlock Text="🎨 Theme Configuration" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Text="Editor Theme:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox x:Name="EditorThemeCombo" Style="{StaticResource SettingComboStyle}">
                            <ComboBoxItem Content="Dark (VS Code)" IsSelected="True"/>
                            <ComboBoxItem Content="Light"/>
                            <ComboBoxItem Content="High Contrast Dark"/>
                            <ComboBoxItem Content="High Contrast Light"/>
                            <ComboBoxItem Content="Monokai"/>
                            <ComboBoxItem Content="Solarized Dark"/>
                            <ComboBoxItem Content="Solarized Light"/>
                        </ComboBox>

                        <TextBlock Text="UI Theme:" Style="{StaticResource SettingLabelStyle}"/>
                        <ComboBox x:Name="UIThemeCombo" Style="{StaticResource SettingComboStyle}">
                            <ComboBoxItem Content="Dark Blue" IsSelected="True"/>
                            <ComboBoxItem Content="Dark Green"/>
                            <ComboBoxItem Content="Dark Purple"/>
                            <ComboBoxItem Content="Dark Red"/>
                            <ComboBoxItem Content="Light"/>
                        </ComboBox>

                        <CheckBox x:Name="SyncThemesCheck" Content="Sync editor and UI themes"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                    </StackPanel>
                </Border>

                <!-- Project Settings -->
                <Border Style="{StaticResource SettingsPanelStyle}">
                    <StackPanel>
                        <TextBlock Text="📁 Project Configuration" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Text="Default Project Path:" Style="{StaticResource SettingLabelStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox x:Name="DefaultProjectPathText" Grid.Column="0"
                                     Style="{StaticResource SettingInputStyle}" Margin="0,0,8,12"
                                     Text="C:\Projects"
                                     ToolTip="Default directory for new projects"/>
                            <ui:GlossyButton Grid.Column="1" Text="Browse" Icon="📂"
                                             GlossyColor="#6B7280" Click="BrowseProjectPath_Click"/>
                        </Grid>

                        <CheckBox x:Name="AutoLoadLastProjectCheck" Content="Auto-load last opened project"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                        <CheckBox x:Name="ShowHiddenFilesCheck" Content="Show hidden files and folders"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="False"/>
                        <CheckBox x:Name="EnableGitIntegrationCheck" Content="Enable Git integration"
                                  Foreground="White" Margin="0,0,0,8" IsChecked="True"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <ui:GlossyButton Text="Test Connection" Icon="🔗" GlossyColor="#10B981"
                             Click="TestConnection_Click" Margin="0,0,12,0"/>
            <ui:GlossyButton Text="Reset to Defaults" Icon="🔄" GlossyColor="#6B7280"
                             Click="ResetDefaults_Click" Margin="0,0,12,0"/>
            <ui:GlossyButton Text="Cancel" Icon="❌" GlossyColor="#EF4444"
                             Click="Cancel_Click" Margin="0,0,12,0"/>
            <ui:GlossyButton Text="Apply &amp; Save" Icon="💾" GlossyColor="#3B82F6"
                             Click="ApplyAndSave_Click"/>
        </StackPanel>
    </Grid>
</Window>
