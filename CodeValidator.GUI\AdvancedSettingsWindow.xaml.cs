using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;

namespace CodeValidator.GUI
{
    public partial class AdvancedSettingsWindow : Window
    {
        private readonly AdvancedEditorWindow _parentWindow;
        private bool _settingsChanged = false;

        public AdvancedSettingsWindow(AdvancedEditorWindow parent)
        {
            InitializeComponent();
            _parentWindow = parent;
            LoadCurrentSettings();
            SetupEventHandlers();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // Load AI settings
                AIProviderCombo.SelectedIndex = GetSettingInt("AIProvider", 0);
                APIEndpointText.Text = GetSettingString("APIEndpoint", "http://localhost:11434");
                ModelNameText.Text = GetSettingString("ModelName", "llama3.2:latest");
                AutoConnectCheck.IsChecked = GetSettingBool("AutoConnect", true);
                EnableStreamingCheck.IsChecked = GetSettingBool("EnableStreaming", true);

                // Load editor settings
                DefaultLanguageCombo.SelectedIndex = GetSettingInt("DefaultLanguage", 0);
                FontSizeSlider.Value = GetSettingDouble("FontSize", 14);
                WordWrapCheck.IsChecked = GetSettingBool("WordWrap", false);
                LineNumbersCheck.IsChecked = GetSettingBool("LineNumbers", true);
                MinimapCheck.IsChecked = GetSettingBool("Minimap", true);
                AutoSaveCheck.IsChecked = GetSettingBool("AutoSave", true);

                // Load theme settings
                EditorThemeCombo.SelectedIndex = GetSettingInt("EditorTheme", 0);
                UIThemeCombo.SelectedIndex = GetSettingInt("UITheme", 0);
                SyncThemesCheck.IsChecked = GetSettingBool("SyncThemes", true);

                // Load project settings
                DefaultProjectPathText.Text = GetSettingString("DefaultProjectPath", @"C:\Projects");
                AutoLoadLastProjectCheck.IsChecked = GetSettingBool("AutoLoadLastProject", true);
                ShowHiddenFilesCheck.IsChecked = GetSettingBool("ShowHiddenFiles", false);
                EnableGitIntegrationCheck.IsChecked = GetSettingBool("EnableGitIntegration", true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading settings: {ex.Message}", "Settings Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void SetupEventHandlers()
        {
            // Font size slider
            FontSizeSlider.ValueChanged += (s, e) =>
            {
                FontSizeLabel.Text = $"{(int)FontSizeSlider.Value}px";
                _settingsChanged = true;
            };

            // AI Provider change
            AIProviderCombo.SelectionChanged += (s, e) =>
            {
                UpdateAPIEndpointForProvider();
                _settingsChanged = true;
            };

            // Mark settings as changed for all other controls
            foreach (var control in new Control[] { APIEndpointText, ModelNameText, DefaultProjectPathText })
            {
                if (control is TextBox textBox)
                    textBox.TextChanged += (s, e) => _settingsChanged = true;
            }

            foreach (var control in new Control[] { AutoConnectCheck, EnableStreamingCheck, WordWrapCheck, 
                LineNumbersCheck, MinimapCheck, AutoSaveCheck, SyncThemesCheck, AutoLoadLastProjectCheck, 
                ShowHiddenFilesCheck, EnableGitIntegrationCheck })
            {
                if (control is CheckBox checkBox)
                    checkBox.Checked += (s, e) => _settingsChanged = true;
                if (control is CheckBox checkBox2)
                    checkBox2.Unchecked += (s, e) => _settingsChanged = true;
            }
        }

        private void UpdateAPIEndpointForProvider()
        {
            var provider = ((ComboBoxItem)AIProviderCombo.SelectedItem)?.Content?.ToString();
            switch (provider)
            {
                case "Ollama (Local)":
                    APIEndpointText.Text = "http://localhost:11434";
                    break;
                case "OpenAI API":
                    APIEndpointText.Text = "https://api.openai.com/v1";
                    break;
                case "Azure OpenAI":
                    APIEndpointText.Text = "https://your-resource.openai.azure.com";
                    break;
                case "Anthropic Claude":
                    APIEndpointText.Text = "https://api.anthropic.com";
                    break;
                case "Google Gemini":
                    APIEndpointText.Text = "https://generativelanguage.googleapis.com";
                    break;
                case "Custom API":
                    APIEndpointText.Text = "";
                    break;
            }
        }

        private async void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as UI.GlossyButton;
            var originalText = button?.Text;
            
            try
            {
                if (button != null)
                {
                    button.Text = "Testing...";
                    button.IsEnabled = false;
                }

                var endpoint = APIEndpointText.Text.Trim();
                if (string.IsNullOrEmpty(endpoint))
                {
                    MessageBox.Show("Please enter an API endpoint to test.", "Test Connection", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                // Test connection based on provider
                var provider = ((ComboBoxItem)AIProviderCombo.SelectedItem)?.Content?.ToString();
                var success = await TestProviderConnection(client, provider, endpoint);

                MessageBox.Show(
                    success ? "✅ Connection successful!" : "❌ Connection failed. Please check your settings.",
                    "Connection Test",
                    MessageBoxButton.OK,
                    success ? MessageBoxImage.Information : MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Connection test failed: {ex.Message}", "Connection Test", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                if (button != null)
                {
                    button.Text = originalText;
                    button.IsEnabled = true;
                }
            }
        }

        private async Task<bool> TestProviderConnection(HttpClient client, string provider, string endpoint)
        {
            try
            {
                switch (provider)
                {
                    case "Ollama (Local)":
                        var response = await client.GetAsync($"{endpoint}/api/tags");
                        return response.IsSuccessStatusCode;
                    
                    case "OpenAI API":
                    case "Azure OpenAI":
                        // For OpenAI, we'd need an API key to test properly
                        var openAIResponse = await client.GetAsync(endpoint);
                        return openAIResponse.StatusCode != System.Net.HttpStatusCode.NotFound;
                    
                    default:
                        // Generic HTTP test
                        var genericResponse = await client.GetAsync(endpoint);
                        return genericResponse.IsSuccessStatusCode;
                }
            }
            catch
            {
                return false;
            }
        }

        private void BrowseProjectPath_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "Select Default Project Directory",
                CheckFileExists = false,
                CheckPathExists = false,
                FileName = "Select Folder",
                Filter = "Folders|no.files",
                ValidateNames = false
            };

            if (dialog.ShowDialog() == true)
            {
                var folderPath = Path.GetDirectoryName(dialog.FileName);
                if (!string.IsNullOrEmpty(folderPath))
                {
                    DefaultProjectPathText.Text = folderPath;
                    _settingsChanged = true;
                }
            }
        }

        private void ResetDefaults_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to reset all settings to their default values?",
                "Reset Settings",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ResetToDefaults();
                _settingsChanged = true;
            }
        }

        private void ResetToDefaults()
        {
            // Reset AI settings
            AIProviderCombo.SelectedIndex = 0;
            APIEndpointText.Text = "http://localhost:11434";
            APIKeyBox.Password = "";
            ModelNameText.Text = "llama3.2:latest";
            AutoConnectCheck.IsChecked = true;
            EnableStreamingCheck.IsChecked = true;

            // Reset editor settings
            DefaultLanguageCombo.SelectedIndex = 0;
            FontSizeSlider.Value = 14;
            WordWrapCheck.IsChecked = false;
            LineNumbersCheck.IsChecked = true;
            MinimapCheck.IsChecked = true;
            AutoSaveCheck.IsChecked = true;

            // Reset theme settings
            EditorThemeCombo.SelectedIndex = 0;
            UIThemeCombo.SelectedIndex = 0;
            SyncThemesCheck.IsChecked = true;

            // Reset project settings
            DefaultProjectPathText.Text = @"C:\Projects";
            AutoLoadLastProjectCheck.IsChecked = true;
            ShowHiddenFilesCheck.IsChecked = false;
            EnableGitIntegrationCheck.IsChecked = true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            if (_settingsChanged)
            {
                var result = MessageBox.Show(
                    "You have unsaved changes. Are you sure you want to cancel?",
                    "Unsaved Changes",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            DialogResult = false;
            Close();
        }

        private void ApplyAndSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveAllSettings();
                ApplySettingsToParent();
                
                MessageBox.Show("✅ Settings saved successfully!", "Settings", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error saving settings: {ex.Message}", "Settings Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveAllSettings()
        {
            // Save AI settings
            SetSetting("AIProvider", AIProviderCombo.SelectedIndex);
            SetSetting("APIEndpoint", APIEndpointText.Text);
            SetSetting("APIKey", APIKeyBox.Password);
            SetSetting("ModelName", ModelNameText.Text);
            SetSetting("AutoConnect", AutoConnectCheck.IsChecked ?? false);
            SetSetting("EnableStreaming", EnableStreamingCheck.IsChecked ?? false);

            // Save editor settings
            SetSetting("DefaultLanguage", DefaultLanguageCombo.SelectedIndex);
            SetSetting("FontSize", FontSizeSlider.Value);
            SetSetting("WordWrap", WordWrapCheck.IsChecked ?? false);
            SetSetting("LineNumbers", LineNumbersCheck.IsChecked ?? true);
            SetSetting("Minimap", MinimapCheck.IsChecked ?? true);
            SetSetting("AutoSave", AutoSaveCheck.IsChecked ?? true);

            // Save theme settings
            SetSetting("EditorTheme", EditorThemeCombo.SelectedIndex);
            SetSetting("UITheme", UIThemeCombo.SelectedIndex);
            SetSetting("SyncThemes", SyncThemesCheck.IsChecked ?? true);

            // Save project settings
            SetSetting("DefaultProjectPath", DefaultProjectPathText.Text);
            SetSetting("AutoLoadLastProject", AutoLoadLastProjectCheck.IsChecked ?? true);
            SetSetting("ShowHiddenFiles", ShowHiddenFilesCheck.IsChecked ?? false);
            SetSetting("EnableGitIntegration", EnableGitIntegrationCheck.IsChecked ?? true);
        }

        private void ApplySettingsToParent()
        {
            // Apply settings to the parent Advanced Editor window
            _parentWindow?.ApplySettings(new AdvancedEditorSettings
            {
                AIProvider = ((ComboBoxItem)AIProviderCombo.SelectedItem)?.Content?.ToString() ?? "Ollama (Local)",
                APIEndpoint = APIEndpointText.Text,
                APIKey = APIKeyBox.Password,
                ModelName = ModelNameText.Text,
                AutoConnect = AutoConnectCheck.IsChecked ?? false,
                EnableStreaming = EnableStreamingCheck.IsChecked ?? false,
                
                DefaultLanguage = ((ComboBoxItem)DefaultLanguageCombo.SelectedItem)?.Content?.ToString() ?? "C#",
                FontSize = (int)FontSizeSlider.Value,
                WordWrap = WordWrapCheck.IsChecked ?? false,
                LineNumbers = LineNumbersCheck.IsChecked ?? true,
                Minimap = MinimapCheck.IsChecked ?? true,
                AutoSave = AutoSaveCheck.IsChecked ?? true,
                
                EditorTheme = ((ComboBoxItem)EditorThemeCombo.SelectedItem)?.Content?.ToString() ?? "Dark (VS Code)",
                UITheme = ((ComboBoxItem)UIThemeCombo.SelectedItem)?.Content?.ToString() ?? "Dark Blue",
                SyncThemes = SyncThemesCheck.IsChecked ?? true,
                
                DefaultProjectPath = DefaultProjectPathText.Text,
                AutoLoadLastProject = AutoLoadLastProjectCheck.IsChecked ?? true,
                ShowHiddenFiles = ShowHiddenFilesCheck.IsChecked ?? false,
                EnableGitIntegration = EnableGitIntegrationCheck.IsChecked ?? true
            });
        }

        // Helper methods for settings management
        private string GetSettingString(string key, string defaultValue)
        {
            return ConfigurationManager.AppSettings[key] ?? defaultValue;
        }

        private int GetSettingInt(string key, int defaultValue)
        {
            return int.TryParse(ConfigurationManager.AppSettings[key], out int value) ? value : defaultValue;
        }

        private double GetSettingDouble(string key, double defaultValue)
        {
            return double.TryParse(ConfigurationManager.AppSettings[key], out double value) ? value : defaultValue;
        }

        private bool GetSettingBool(string key, bool defaultValue)
        {
            return bool.TryParse(ConfigurationManager.AppSettings[key], out bool value) ? value : defaultValue;
        }

        private void SetSetting(string key, object value)
        {
            // In a real application, you'd save to app.config or a settings file
            // For now, we'll just store in memory or use Properties.Settings
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                if (config.AppSettings.Settings[key] != null)
                    config.AppSettings.Settings[key].Value = value.ToString();
                else
                    config.AppSettings.Settings.Add(key, value.ToString());
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
            }
            catch
            {
                // Fallback: store in Properties.Settings if available
            }
        }
    }

    public class AdvancedEditorSettings
    {
        // AI Settings
        public string AIProvider { get; set; } = "Ollama (Local)";
        public string APIEndpoint { get; set; } = "http://localhost:11434";
        public string APIKey { get; set; } = "";
        public string ModelName { get; set; } = "llama3.2:latest";
        public bool AutoConnect { get; set; } = true;
        public bool EnableStreaming { get; set; } = true;

        // Editor Settings
        public string DefaultLanguage { get; set; } = "C#";
        public int FontSize { get; set; } = 14;
        public bool WordWrap { get; set; } = false;
        public bool LineNumbers { get; set; } = true;
        public bool Minimap { get; set; } = true;
        public bool AutoSave { get; set; } = true;

        // Theme Settings
        public string EditorTheme { get; set; } = "Dark (VS Code)";
        public string UITheme { get; set; } = "Dark Blue";
        public bool SyncThemes { get; set; } = true;

        // Project Settings
        public string DefaultProjectPath { get; set; } = @"C:\Projects";
        public bool AutoLoadLastProject { get; set; } = true;
        public bool ShowHiddenFiles { get; set; } = false;
        public bool EnableGitIntegration { get; set; } = true;
    }
}
