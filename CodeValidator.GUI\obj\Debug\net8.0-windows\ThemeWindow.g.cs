﻿#pragma checksum "..\..\..\ThemeWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DC271A34FABAB813A688075EB2208B8C84409E46"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CodeValidator.GUI {
    
    
    /// <summary>
    /// ThemeWindow
    /// </summary>
    public partial class ThemeWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 41 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle EditorBgPreview;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EditorBgColor;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle EditorTextPreview;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EditorTextColor;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle LineNumberPreview;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LineNumberColor;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle SelectionPreview;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SelectionColor;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle ChatBgPreview;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ChatBgColor;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle UserMsgPreview;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UserMsgColor;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle AIMsgPreview;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AIMsgColor;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle ChatTextPreview;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ChatTextColor;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle SidebarBgPreview;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SidebarBgColor;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle ButtonPreview;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ButtonColor;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle AccentPreview;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\ThemeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccentColor;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CodeValidator.GUI;component/themewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ThemeWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.EditorBgPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 2:
            this.EditorBgColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.EditorTextPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 4:
            this.EditorTextColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.LineNumberPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 6:
            this.LineNumberColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.SelectionPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 8:
            this.SelectionColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.ChatBgPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 10:
            this.ChatBgColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.UserMsgPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 12:
            this.UserMsgColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.AIMsgPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 14:
            this.AIMsgColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ChatTextPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 16:
            this.ChatTextColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.SidebarBgPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 18:
            this.SidebarBgColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.ButtonPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 20:
            this.ButtonColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.AccentPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 22:
            this.AccentColor = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            
            #line 123 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyDarkTheme_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 124 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyVSCodeTheme_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 125 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyMatrixTheme_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 126 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyOceanTheme_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 127 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyDraculaTheme_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 128 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyMonokaiTheme_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 131 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyGlossyBlueTheme_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 141 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyGlossyGreenTheme_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 151 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyGlossyPurpleTheme_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 161 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyGlossyRedTheme_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 171 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyGlossyCharcoalTheme_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 187 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            
            #line 189 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveTheme_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            
            #line 191 "..\..\..\ThemeWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

